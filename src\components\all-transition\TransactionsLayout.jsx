"use client";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import React, { useEffect, useState } from "react";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "../ui";
import Badge from "../ui/badge/Badge";
import tableDesImg from "../../../public/assets/user/product-01.webp";
import Image from "next/image";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

function TransactionsLayout() {
  // use network
  const networkService = new NetworkService();
  const [transitionData, setTransitionData] = useState([]);
  const [loading, setLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState();
  const [lastPage, setLastPage] = useState(1);

  // Fetch transition data
  const fetchTransitionData = async () => {
    setLoading(true);
    try {
      const res = await networkService.get(ApiPath.recentTransition, {
        page: currentPage,
        per_page: perPage,
      });
      if (res.status === "completed") {
        setTransitionData(res.data.data.transactions);
        const meta = res.data.data.meta;
        setPerPage(meta.per_page);
        setLastPage(meta.last_page);
        setCurrentPage(meta.current_page);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransitionData();
  }, [currentPage]);

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < lastPage) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePageClick = (pageNum) => {
    setCurrentPage(pageNum);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Transactions
        </h1>
        <div className="flex space-x-3">
          <button className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Export
          </button>
          <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Filter
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Transaction History
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            View all your transaction history and details.
          </p>
        </div>
        <div className="p-0">
          <div className="overflow-x-auto">
            <Table>
              {/* Table Header */}
              <TableHeader className="border-b bg-gray-50 border-gray-100 dark:border-white/[0.05]">
                <TableRow>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Description
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Transaction ID
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Type
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Amount
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Charge
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Status
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Method
                  </TableCell>
                </TableRow>
              </TableHeader>

              {/* Table Body */}
              <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                {loading ? (
                  <tr>
                    <td colSpan={7} className="text-center text-gray-400 py-6">
                      Loading...
                    </td>
                  </tr>
                ) : transitionData.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center text-gray-400 py-6">
                      No transactions found.
                    </td>
                  </tr>
                ) : (
                  transitionData.map((item) => (
                    <TableRow key={item.tnx}>
                      <TableCell className="px-5 py-4 sm:px-6 text-start">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 overflow-hidden rounded-full">
                            <Image
                              width={40}
                              height={40}
                              src={tableDesImg}
                              alt="Transaction"
                            />
                          </div>
                          <div>
                            <span className="block font-medium text-gray-800 text-theme-sm dark:text-white/90">
                              {item?.description}
                            </span>
                            <span className="block text-gray-500 text-theme-xs dark:text-gray-400">
                              {item?.created_at}
                            </span>
                          </div>
                        </div>
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {item?.tnx}
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {item?.type}
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        <span
                          className={
                            item.is_plus ? "text-green-700" : "text-red-700"
                          }
                        >
                          {item.is_plus ? "+" : "-"}
                          {item.amount}
                          <span className="ml-1 text-xs">
                            {item.is_plus ? "↑" : "↓"}
                          </span>
                        </span>
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {item?.charge}
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        <Badge
                          size="sm"
                          variant="solid"
                          color={
                            item?.status === "Success"
                              ? "success"
                              : item?.status === "Pending"
                              ? "warning"
                              : "error"
                          }
                        >
                          {item?.status}
                        </Badge>
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {item?.method}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            {/* Pagination */}
            {transitionData.length > 0 && lastPage > 1 && (
              <div className="mt-6 flex items-center justify-between border-t border-gray-200 p-4">
                <div className="flex items-center space-x-4">
                  {/* Previous Button */}
                  <button
                    onClick={handlePrevPage}
                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={currentPage === 1}
                  >
                    <ChevronLeftIcon className="w-3 h-3" />
                    Previous
                  </button>

                  {/* Page Numbers */}
                  <div className="flex items-center space-x-2">
                    {[...Array(lastPage)].map((_, i) => {
                      const pageNum = i + 1;
                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageClick(pageNum)}
                          className={`inline-flex items-center justify-center w-8 h-8 text-sm font-medium rounded-lg hover:bg-gray-50 hover:text-gray-900 ${
                            currentPage === pageNum
                              ? "bg-blue-600 text-white hover:"
                              : "text-gray-700 bg-white border border-gray-300"
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                  </div>

                  {/* Next Button */}
                  <button
                    onClick={handleNextPage}
                    disabled={currentPage === lastPage}
                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 focus:outline-none"
                  >
                    Next
                    <ChevronRightIcon className="w-3 h-3" />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default TransactionsLayout;
