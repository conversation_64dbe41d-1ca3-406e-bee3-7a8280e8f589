"use client";
import Cookies from "js-cookie";
import { createContext, useContext, useEffect, useState } from "react";
import ApiPath from "../network/api/api_path";
import NetworkService from "../network/service/network_service";

// create context
const UserContext = createContext();

// create custom hook
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};

// create provider
export const UserProvider = ({ children }) => {
  const network = new NetworkService();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // fetch user
  const fetchUser = async () => {
    try {
      setLoading(true);
      const res = await network.get(ApiPath.userProfile);
      setUser(res.data.data.user);
    } catch (err) {
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // update user
  const updateUser = (newUserData) => {
    setUser((prevUser) => ({
      ...prevUser,
      ...newUserData,
    }));
  };

  const refreshUser = () => {
    fetchUser();
  };

  // logout
  const logout = () => {
    network.tokenService.clearToken();
    setUser(null);
    setLoading(false);
    window.location.href = "/auth/signin";
  };

  useEffect(() => {
    const token = Cookies.get("token");
    if (token) {
      fetchUser();
    } else {
      setLoading(false);
    }
  }, []);

  return (
    <UserContext.Provider
      value={{
        user,
        loading,
        updateUser,
        refreshUser,
        fetchUser,
        logout,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};
