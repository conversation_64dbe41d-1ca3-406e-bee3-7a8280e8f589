import { NextResponse } from "next/server";

export function middleware(request) {
  // cookie token
  const token = request.cookies.get("token")?.value;
  const pathname = request.nextUrl?.pathname || "";

  // Redirect logged-in users away from login page
  if (token && pathname.startsWith("/auth")) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Routes that need authentication
  const protectedPaths = ["/dashboard", "/profile", "/settings"];

  // Redirect unauthenticated users to login
  if (!token && protectedPaths.some((path) => pathname.startsWith(path))) {
    return NextResponse.redirect(new URL("/auth/signin", request.url));
  }

  return NextResponse.next();
}

// Make middleware run for protected routes and login page
export const config = {
  matcher: ["/auth/login", "/dashboard/:path*", "/auth/:path*"],
};
